import 'package:flutter/material.dart';
import 'package:v_chat_input_ui/v_chat_input_ui.dart';

/// Widget to display sticker messages in chat
class StickerMessageWidget extends StatelessWidget {
  final bool isMeSender;
  final Map<String, dynamic> data;

  const StickerMessageWidget({
    super.key,
    required this.isMeSender,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final assetPath = data['assetPath'] as String?;
    final stickerName = data['name'] as String?;
    final emoji = data['emoji'] as String?;

    if (assetPath == null || stickerName == null) {
      // Fallback for invalid sticker data
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'Invalid sticker',
          style: TextStyle(
            color: Colors.red,
            fontSize: 14,
          ),
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(
        maxWidth: 120,
        maxHeight: 120,
      ),
      child: StickerWidget(
        assetPath: assetPath,
        emoji: emoji ?? '🎭',
        name: stickerName,
        size: 100,
        onTap: () {
          // Optional: Show sticker details or add to favorites
          _showStickerDetails(context);
        },
      ),
    );
  }

  void _showStickerDetails(BuildContext context) {
    final stickerName = data['name'] as String?;

    if (stickerName != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Sticker: $stickerName'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
}
