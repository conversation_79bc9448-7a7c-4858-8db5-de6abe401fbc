name: v_chat_input_ui
description: v chat text input ui and recorder this item is part of v_chat_sdk and it can be used as a standalone package
version: 1.2.1
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/v-chat-sdk/v_chat_input_ui/issues
repository: https://github.com/v-chat-sdk/v_chat_input_ui
publish_to: none

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"
dependencies:
  flutter:
    sdk: flutter
  permission_handler: ^11.3.1
  stop_watch_timer: ^3.1.1
  place_picker_v2: ^0.0.2
  #  google_maps_place_picker_mb: ^3.0.1
  latlong2: ^0.9.1
  super_up_core:
    path: ../../packages/super_up_core
  cross_file: ^0.3.4+2
#  record_without_android: ^5.1.1
  v_chat_mention_controller: ^1.0.0
  emoji_picker_flutter: ^3.1.0
  meta: ^1.15.0
  record: 5.1.2
  intl: ^0.20.2
  v_platform: ^2.1.4
  cached_network_image: ^3.4.1
  adaptive_dialog: ^2.2.1+2
  path: ^1.9.0
  http: ^1.3.0
  textless:
    git:
      url: https://github.com/hatemragab/textless.git
  phosphor_flutter: ^2.1.0
  file_picker: ^8.1.3
  image_cropper: ^8.0.2
  wechat_camera_picker: ^4.3.6
  uuid: ^4.5.1
  path_provider: ^2.1.5
  v_chat_sdk_core:
    path: ../../packages/v_chat_sdk_core
#  flutter_desktop_audio_recorder: ^0.0.1
dev_dependencies:
  flutter_lints: ^5.0.0
  lints: ^5.0.0

flutter:
  assets:
    - assets/stickers/emotions/
    - assets/stickers/animals/
    - assets/stickers/actions/
    - assets/stickers/custom_pack/

platforms:
  android:
  ios:
  web:
  windows:
  macos:
