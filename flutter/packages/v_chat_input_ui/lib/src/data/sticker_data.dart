// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import '../models/sticker_models.dart';

/// Default sticker data provider
class VStickerData {
  static const String _basePath = 'packages/v_chat_input_ui/assets/stickers';

  /// Get all available sticker packs
  static List<VStickerPack> getDefaultStickerPacks() {
    return [
      _getEmotionsPack(),
      _getAnimalsPack(),
      _getActionsPack(),
    ];
  }

  /// Emotions sticker pack
  static VStickerPack _getEmotionsPack() {
    return VStickerPack(
      id: 'emotions',
      name: 'Emotions',
      iconPath: '$_basePath/emotions/happy.png',
      isDefault: true,
      stickers: [
        VSticker(
          id: 'happy',
          name: 'Happy',
          assetPath: '$_basePath/emotions/happy.png',
          emoji: '😊',
          tags: ['happy', 'smile', 'joy'],
        ),
        VSticker(
          id: 'sad',
          name: 'Sad',
          assetPath: '$_basePath/emotions/sad.png',
          emoji: '😢',
          tags: ['sad', 'cry', 'tears'],
        ),
        VSticker(
          id: 'angry',
          name: 'Angry',
          assetPath: '$_basePath/emotions/angry.png',
          emoji: '😠',
          tags: ['angry', 'mad', 'rage'],
        ),
        VSticker(
          id: 'love',
          name: 'Love',
          assetPath: '$_basePath/emotions/love.png',
          emoji: '😍',
          tags: ['love', 'heart', 'romance'],
        ),
        VSticker(
          id: 'surprised',
          name: 'Surprised',
          assetPath: '$_basePath/emotions/surprised.png',
          emoji: '😲',
          tags: ['surprised', 'shock', 'wow'],
        ),
        VSticker(
          id: 'thinking',
          name: 'Thinking',
          assetPath: '$_basePath/emotions/thinking.png',
          emoji: '🤔',
          tags: ['thinking', 'wonder', 'hmm'],
        ),
      ],
    );
  }

  /// Animals sticker pack
  static VStickerPack _getAnimalsPack() {
    return VStickerPack(
      id: 'animals',
      name: 'Animals',
      iconPath: '$_basePath/animals/cat.png',
      stickers: [
        VSticker(
          id: 'cat',
          name: 'Cat',
          assetPath: '$_basePath/animals/cat.png',
          emoji: '🐱',
          tags: ['cat', 'kitten', 'pet'],
        ),
        VSticker(
          id: 'dog',
          name: 'Dog',
          assetPath: '$_basePath/animals/dog.png',
          emoji: '🐶',
          tags: ['dog', 'puppy', 'pet'],
        ),
        VSticker(
          id: 'bear',
          name: 'Bear',
          assetPath: '$_basePath/animals/bear.png',
          emoji: '🐻',
          tags: ['bear', 'teddy', 'cute'],
        ),
        VSticker(
          id: 'rabbit',
          name: 'Rabbit',
          assetPath: '$_basePath/animals/rabbit.png',
          emoji: '🐰',
          tags: ['rabbit', 'bunny', 'hop'],
        ),
      ],
    );
  }

  /// Actions sticker pack
  static VStickerPack _getActionsPack() {
    return VStickerPack(
      id: 'actions',
      name: 'Actions',
      iconPath: '$_basePath/actions/thumbs_up.png',
      stickers: [
        VSticker(
          id: 'thumbs_up',
          name: 'Thumbs Up',
          assetPath: '$_basePath/actions/thumbs_up.png',
          emoji: '👍',
          tags: ['thumbs', 'up', 'good', 'ok'],
        ),
        VSticker(
          id: 'thumbs_down',
          name: 'Thumbs Down',
          assetPath: '$_basePath/actions/thumbs_down.png',
          emoji: '👎',
          tags: ['thumbs', 'down', 'bad', 'no'],
        ),
        VSticker(
          id: 'clap',
          name: 'Clap',
          assetPath: '$_basePath/actions/clap.png',
          emoji: '👏',
          tags: ['clap', 'applause', 'good'],
        ),
        VSticker(
          id: 'wave',
          name: 'Wave',
          assetPath: '$_basePath/actions/wave.png',
          emoji: '👋',
          tags: ['wave', 'hello', 'hi', 'bye'],
        ),
      ],
    );
  }

  /// Search stickers by query
  static List<VSticker> searchStickers(String query, List<VStickerPack> packs) {
    if (query.isEmpty) return [];
    
    final results = <VSticker>[];
    final lowerQuery = query.toLowerCase();
    
    for (final pack in packs) {
      for (final sticker in pack.stickers) {
        if (sticker.name.toLowerCase().contains(lowerQuery) ||
            sticker.tags.any((tag) => tag.toLowerCase().contains(lowerQuery))) {
          results.add(sticker);
        }
      }
    }
    
    return results;
  }
}
